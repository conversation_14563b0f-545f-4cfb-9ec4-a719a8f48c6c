defmodule Starlight.ThreeMoment.MatrixBuilder do
  @moduledoc """
  Matrix construction for the Three-Moment Method equations.

  This module builds the flexibility matrix and load vector for solving
  the three-moment equations. It handles the conversion from MATLAB's
  1-based indexing to Elixir's 0-based indexing.
  """

  alias Starlight.ThreeMoment.Types

  @doc """
  Builds the flexibility matrix for the three-moment equations.

  The flexibility matrix is a tridiagonal matrix where:
  - F[0,0] = 1.0 (boundary condition)
  - F[N,N] = 1.0 (boundary condition)
  - For interior nodes j (1 to N-1):
    - F[j, j-1] = L[j-1] / I[j-1]
    - F[j, j] = 2 * (L[j-1]/I[j-1] + L[j]/I[j])
    - F[j, j+1] = L[j] / I[j]

  ## Parameters
  - `span_lengths`: Tensor of span lengths
  - `moments_of_inertia`: Tensor of moment of inertia values

  ## Returns
  - `{:ok, flexibility_matrix}` - (N+1) x (N+1) flexibility matrix
  """
  @spec build_flexibility_matrix(Nx.Tensor.t(), Nx.Tensor.t()) :: {:ok, Nx.Tensor.t()}
  def build_flexibility_matrix(span_lengths, moments_of_inertia) do
    n_spans = Nx.size(span_lengths)
    n_nodes = n_spans + 1

    # Initialize flexibility matrix with zeros
    f_matrix = Nx.broadcast(0.0, {n_nodes, n_nodes})

    # Set boundary conditions
    f_matrix = Nx.put_slice(f_matrix, [0, 0], Nx.tensor([[1.0]]))
    f_matrix = Nx.put_slice(f_matrix, [n_nodes - 1, n_nodes - 1], Nx.tensor([[1.0]]))

    # Build interior rows (j = 1 to N-1, which is indices 1 to n_spans-1)
    f_matrix = build_interior_rows(f_matrix, span_lengths, moments_of_inertia, n_spans)

    {:ok, f_matrix}
  end

  @doc """
  Builds the load vector for the three-moment equations.

  The load vector accounts for distributed loads and point loads:
  - d[0] = 0.0 (boundary condition)
  - d[N] = 0.0 (boundary condition)
  - For interior nodes j (1 to N-1):
    - d[j] = -w[j-1]*L[j-1]^3/(4*I[j-1]) - w[j]*L[j]^3/(4*I[j])
    - Plus contributions from point loads in adjacent spans

  ## Parameters
  - `span_lengths`: Tensor of span lengths
  - `moments_of_inertia`: Tensor of moment of inertia values
  - `distributed_loads`: Tensor of distributed loads per span
  - `point_load_info`: Point load information from LoadAnalysis
  - `point_loads`: Tensor of point load magnitudes

  ## Returns
  - `{:ok, load_vector}` - (N+1) x 1 load vector
  """
  @spec build_load_vector(
          Nx.Tensor.t(),
          Nx.Tensor.t(),
          Nx.Tensor.t(),
          Types.point_load_info(),
          Nx.Tensor.t(),
          boolean()
        ) :: {:ok, Nx.Tensor.t()}
  def build_load_vector(
        span_lengths,
        moments_of_inertia,
        distributed_loads,
        point_load_info,
        point_loads,
        has_point_loads \\ true
      ) do
    n_spans = Nx.size(span_lengths)
    n_nodes = n_spans + 1

    # Initialize load vector with zeros
    d_vector = Nx.broadcast(0.0, {n_nodes})

    # Build interior elements (j = 1 to N-1, which is indices 1 to n_spans-1)
    d_vector =
      build_distributed_load_contributions(
        d_vector,
        span_lengths,
        moments_of_inertia,
        distributed_loads,
        n_spans
      )

    # Add point load contributions
    d_vector =
      if has_point_loads do
        add_point_load_contributions(
          d_vector,
          span_lengths,
          moments_of_inertia,
          point_load_info,
          point_loads,
          n_spans
        )
      else
        d_vector
      end

    {:ok, d_vector}
  end

  # Private helper functions

  defp build_interior_rows(f_matrix, span_lengths, moments_of_inertia, n_spans) do
    # For each interior node j (1 to n_spans-1)
    if n_spans <= 1 do
      # No interior nodes for single span
      f_matrix
    else
      Enum.reduce(1..(n_spans - 1), f_matrix, fn j, acc_matrix ->
        # Left span index (j-1), right span index (j)
        left_idx = j - 1
        right_idx = j

        # Get span properties
        l_left = Nx.slice(span_lengths, [left_idx], [1]) |> Nx.squeeze()
        i_left = Nx.slice(moments_of_inertia, [left_idx], [1]) |> Nx.squeeze()
        l_right = Nx.slice(span_lengths, [right_idx], [1]) |> Nx.squeeze()
        i_right = Nx.slice(moments_of_inertia, [right_idx], [1]) |> Nx.squeeze()

        # Calculate matrix elements
        # F[j, j-1]
        f_left = Nx.divide(l_left, i_left)
        # F[j, j]
        f_center =
          Nx.multiply(2.0, Nx.add(Nx.divide(l_left, i_left), Nx.divide(l_right, i_right)))

        # F[j, j+1]
        f_right = Nx.divide(l_right, i_right)

        # Set matrix elements
        acc_matrix
        |> Nx.put_slice([j, j - 1], Nx.reshape(f_left, {1, 1}))
        |> Nx.put_slice([j, j], Nx.reshape(f_center, {1, 1}))
        |> Nx.put_slice([j, j + 1], Nx.reshape(f_right, {1, 1}))
      end)
    end
  end

  defp build_distributed_load_contributions(
         d_vector,
         span_lengths,
         moments_of_inertia,
         distributed_loads,
         n_spans
       ) do
    # For each interior node j (1 to n_spans-1)
    if n_spans <= 1 do
      # No interior nodes for single span
      d_vector
    else
      Enum.reduce(1..(n_spans - 1), d_vector, fn j, acc_vector ->
        # Left span index (j-1), right span index (j)
        left_idx = j - 1
        right_idx = j

        # Get span properties
        l_left = Nx.slice(span_lengths, [left_idx], [1]) |> Nx.squeeze()
        i_left = Nx.slice(moments_of_inertia, [left_idx], [1]) |> Nx.squeeze()
        w_left = Nx.slice(distributed_loads, [left_idx], [1]) |> Nx.squeeze()

        l_right = Nx.slice(span_lengths, [right_idx], [1]) |> Nx.squeeze()
        i_right = Nx.slice(moments_of_inertia, [right_idx], [1]) |> Nx.squeeze()
        w_right = Nx.slice(distributed_loads, [right_idx], [1]) |> Nx.squeeze()

        # Calculate distributed load contribution
        # d[j] = -w_left*L_left^3/(4*I_left) - w_right*L_right^3/(4*I_right)
        left_contrib =
          l_left
          |> Nx.pow(3)
          |> Nx.multiply(w_left)
          |> Nx.divide(Nx.multiply(4.0, i_left))
          |> Nx.negate()

        right_contrib =
          l_right
          |> Nx.pow(3)
          |> Nx.multiply(w_right)
          |> Nx.divide(Nx.multiply(4.0, i_right))
          |> Nx.negate()

        total_contrib = Nx.add(left_contrib, right_contrib)

        # Update the load vector
        Nx.put_slice(acc_vector, [j], Nx.reshape(total_contrib, {1}))
      end)
    end
  end

  defp add_point_load_contributions(
         d_vector,
         span_lengths,
         moments_of_inertia,
         point_load_info,
         point_loads,
         n_spans
       ) do
    n_points = Nx.size(point_loads)

    if n_points == 0 do
      d_vector
    else
      # For each interior node j (1 to n_spans-1)
      if n_spans <= 1 do
        # No interior nodes for single span
        d_vector
      else
        Enum.reduce(1..(n_spans - 1), d_vector, fn j, acc_vector ->
          left_span_idx = j - 1
          right_span_idx = j

          # Calculate contributions from point loads in left and right spans
          left_contrib =
            calculate_point_load_contribution_for_span(
              left_span_idx,
              j,
              span_lengths,
              moments_of_inertia,
              point_load_info,
              point_loads,
              :left
            )

          right_contrib =
            calculate_point_load_contribution_for_span(
              right_span_idx,
              j,
              span_lengths,
              moments_of_inertia,
              point_load_info,
              point_loads,
              :right
            )

          total_contrib = Nx.add(left_contrib, right_contrib)
          current_value = Nx.slice(acc_vector, [j], [1]) |> Nx.squeeze()
          new_value = Nx.add(current_value, total_contrib)

          Nx.put_slice(acc_vector, [j], Nx.reshape(new_value, {1}))
        end)
      end
    end
  end

  defp calculate_point_load_contribution_for_span(
         span_idx,
         _node_idx,
         span_lengths,
         moments_of_inertia,
         point_load_info,
         point_loads,
         side
       ) do
    # Find point loads in this span
    span_mask = Nx.equal(point_load_info.spans, span_idx)

    if Nx.any(span_mask) |> Nx.to_number() == 0 do
      Nx.tensor(0.0)
    else
      # Get span properties
      l_span = Nx.slice(span_lengths, [span_idx], [1]) |> Nx.squeeze()
      i_span = Nx.slice(moments_of_inertia, [span_idx], [1]) |> Nx.squeeze()

      # Get distances based on which side of the node we're considering
      distances =
        case side do
          :left -> point_load_info.distances_left
          :right -> point_load_info.distances_right
        end

      # Calculate contribution for each point load in this span
      masked_loads = Nx.select(span_mask, point_loads, 0.0)
      masked_distances = Nx.select(span_mask, distances, 0.0)

      # Formula: -P * x / (L * I) * (L^2 - x^2)
      l_squared = Nx.pow(l_span, 2)
      x_squared = Nx.pow(masked_distances, 2)

      contribution =
        masked_loads
        |> Nx.multiply(masked_distances)
        |> Nx.divide(Nx.multiply(l_span, i_span))
        |> Nx.multiply(Nx.subtract(l_squared, x_squared))
        |> Nx.negate()
        |> Nx.sum()

      contribution
    end
  end
end
