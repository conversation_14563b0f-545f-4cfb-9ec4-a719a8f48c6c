defmodule Starlight.ThreeMoment.DiagramGenerator do
  @moduledoc """
  Diagram generation for the Three-Moment Method analysis.

  This module generates shear, moment, slope, and deflection diagrams
  for continuous beams. It converts the MATLAB plotting functionality
  to Elixir/Nx tensor operations.
  """

  alias Starlight.ThreeMoment.Types

  @default_points_per_span 158

  @doc """
  Generates complete diagrams for shear, moment, slope, and deflection.

  ## Parameters
  - `moments`: Internal moments at supports
  - `span_lengths`: Tensor of span lengths
  - `moments_of_inertia`: Tensor of moment of inertia values
  - `elastic_modulus`: Scalar elastic modulus value
  - `distributed_loads`: Tensor of distributed loads per span
  - `point_load_info`: Point load information from LoadAnalysis
  - `point_loads`: Tensor of point load magnitudes
  - `points_per_span`: Number of points to generate per span (default: 158)

  ## Returns
  - `{:ok, diagram_data}` containing x-coordinates and diagram values
  """
  @spec generate_diagrams(
          Nx.Tensor.t(),
          Nx.Tensor.t(),
          Nx.Tensor.t(),
          number(),
          Nx.Tensor.t(),
          Types.point_load_info(),
          Nx.Tensor.t(),
          integer()
        ) :: {:ok, map()} | {:error, String.t()}
  def generate_diagrams(
        moments,
        span_lengths,
        moments_of_inertia,
        elastic_modulus,
        distributed_loads,
        point_load_info,
        point_loads,
        points_per_span \\ @default_points_per_span
      ) do
    _n_spans = Nx.size(span_lengths)

    with {:ok, x_coords} <- generate_x_coordinates(span_lengths, points_per_span),
         {:ok, slopes} <-
           calculate_slopes(
             moments,
             span_lengths,
             moments_of_inertia,
             elastic_modulus,
             distributed_loads,
             point_load_info,
             point_loads
           ),
         {:ok, shear_diagram} <-
           generate_shear_diagram(
             moments,
             span_lengths,
             distributed_loads,
             point_load_info,
             point_loads,
             x_coords
           ),
         {:ok, moment_diagram} <- generate_moment_diagram(moments, shear_diagram, x_coords),
         {:ok, slope_diagram} <-
           generate_slope_diagram(
             moment_diagram,
             moments_of_inertia,
             elastic_modulus,
             slopes,
             x_coords
           ),
         {:ok, deflection_diagram} <- generate_deflection_diagram(slope_diagram, x_coords) do
      max_deflection =
        deflection_diagram
        |> Nx.abs()
        |> Nx.reduce_max()
        |> Nx.to_number()

      {:ok,
       %{
         x_coordinates: flatten_coordinates(x_coords, span_lengths),
         shear_diagram: Nx.flatten(shear_diagram),
         moment_diagram: Nx.flatten(moment_diagram),
         slope_diagram: Nx.flatten(slope_diagram),
         deflection_diagram: Nx.flatten(deflection_diagram),
         max_deflection: max_deflection
       }}
    end
  end

  @doc """
  Generates x-coordinates for each span.

  Creates evenly spaced points within each span for diagram generation.

  ## Parameters
  - `span_lengths`: Tensor of span lengths
  - `points_per_span`: Number of points per span

  ## Returns
  - `{:ok, x_coordinates}` tensor of shape {points_per_span, n_spans}
  """
  @spec generate_x_coordinates(Nx.Tensor.t(), integer()) :: {:ok, Nx.Tensor.t()}
  def generate_x_coordinates(span_lengths, points_per_span) do
    n_spans = Nx.size(span_lengths)

    # Create x-coordinates for each span (0 to L for each span)
    x_coords =
      Enum.reduce(0..(n_spans - 1), [], fn span_idx, acc ->
        l_span = Nx.slice(span_lengths, [span_idx], [1]) |> Nx.squeeze() |> Nx.to_number()

        # Generate evenly spaced points from 0 to L
        span_x =
          0..(points_per_span - 1)
          |> Enum.map(fn i -> i * l_span / (points_per_span - 1) end)
          |> Nx.tensor()

        [span_x | acc]
      end)
      |> Enum.reverse()
      |> Nx.stack(axis: 1)

    {:ok, x_coords}
  end

  @doc """
  Calculates slopes at support points using the three-moment method.

  ## Parameters
  - `moments`: Internal moments at supports
  - `span_lengths`: Tensor of span lengths
  - `moments_of_inertia`: Tensor of moment of inertia values
  - `elastic_modulus`: Scalar elastic modulus value
  - `distributed_loads`: Tensor of distributed loads per span
  - `point_load_info`: Point load information
  - `point_loads`: Tensor of point load magnitudes

  ## Returns
  - `{:ok, slopes}` tensor of slopes at support points
  """
  @spec calculate_slopes(
          Nx.Tensor.t(),
          Nx.Tensor.t(),
          Nx.Tensor.t(),
          number(),
          Nx.Tensor.t(),
          Types.point_load_info(),
          Nx.Tensor.t()
        ) :: {:ok, Nx.Tensor.t()}
  def calculate_slopes(
        moments,
        span_lengths,
        moments_of_inertia,
        elastic_modulus,
        distributed_loads,
        point_load_info,
        point_loads
      ) do
    n_spans = Nx.size(span_lengths)

    slopes =
      Enum.reduce(0..(n_spans - 1), [], fn j, acc ->
        slope =
          calculate_single_slope(
            j,
            moments,
            span_lengths,
            moments_of_inertia,
            elastic_modulus,
            distributed_loads,
            point_load_info,
            point_loads
          )

        [Nx.to_number(slope) | acc]
      end)
      |> Enum.reverse()
      |> Nx.tensor()

    {:ok, slopes}
  end

  # Private helper functions

  defp calculate_single_slope(
         span_idx,
         moments,
         span_lengths,
         moments_of_inertia,
         elastic_modulus,
         distributed_loads,
         point_load_info,
         point_loads
       ) do
    # Get span properties
    l_span = Nx.slice(span_lengths, [span_idx], [1]) |> Nx.squeeze()
    i_span = Nx.slice(moments_of_inertia, [span_idx], [1]) |> Nx.squeeze()
    w_span = Nx.slice(distributed_loads, [span_idx], [1]) |> Nx.squeeze()

    # Get moments at left and right ends of span
    m_left = Nx.slice(moments, [span_idx], [1]) |> Nx.squeeze()
    m_right = Nx.slice(moments, [span_idx + 1], [1]) |> Nx.squeeze()

    # Calculate base slope from distributed load and moments
    # slope = w*L^3/24 + M_right*L/6 + M_left*L/3
    base_slope =
      w_span
      |> Nx.multiply(Nx.pow(l_span, 3))
      |> Nx.divide(24.0)
      |> Nx.add(Nx.multiply(m_right, l_span) |> Nx.divide(6.0))
      |> Nx.add(Nx.multiply(m_left, l_span) |> Nx.divide(3.0))

    # Add point load contributions
    point_contrib =
      calculate_point_load_slope_contribution(span_idx, l_span, point_load_info, point_loads)

    total_slope = Nx.add(base_slope, point_contrib)

    # Apply negative sign and divide by EI
    total_slope
    |> Nx.negate()
    |> Nx.divide(Nx.multiply(elastic_modulus, i_span))
  end

  defp calculate_point_load_slope_contribution(span_idx, l_span, point_load_info, point_loads) do
    if Nx.size(point_loads) == 0 do
      Nx.tensor(0.0)
    else
      # Find point loads in this span
      span_mask = Nx.equal(point_load_info.spans, span_idx)

      if Nx.any(span_mask) |> Nx.to_number() == 0 do
        Nx.tensor(0.0)
      else
        masked_loads = Nx.select(span_mask, point_loads, 0.0)
        masked_distances_right = Nx.select(span_mask, point_load_info.distances_right, 0.0)

        # Formula: P * xR / L * (L^2 - xR^2) / 6
        l_squared = Nx.pow(l_span, 2)
        xr_squared = Nx.pow(masked_distances_right, 2)

        contribution =
          masked_loads
          |> Nx.multiply(masked_distances_right)
          |> Nx.divide(l_span)
          |> Nx.multiply(Nx.subtract(l_squared, xr_squared))
          |> Nx.divide(6.0)
          |> Nx.sum()

        contribution
      end
    end
  end

  defp generate_shear_diagram(
         moments,
         span_lengths,
         distributed_loads,
         point_load_info,
         point_loads,
         x_coords
       ) do
    n_spans = Nx.size(span_lengths)
    {points_per_span, _} = Nx.shape(x_coords)

    shear_diagram = Nx.broadcast(0.0, {points_per_span, n_spans})

    shear_diagram =
      Enum.reduce(0..(n_spans - 1), shear_diagram, fn span_idx, acc_diagram ->
        span_shear =
          calculate_span_shear_diagram(
            span_idx,
            moments,
            span_lengths,
            distributed_loads,
            point_load_info,
            point_loads,
            x_coords
          )

        Nx.put_slice(acc_diagram, [0, span_idx], Nx.reshape(span_shear, {:auto, 1}))
      end)

    {:ok, shear_diagram}
  end

  defp calculate_span_shear_diagram(
         span_idx,
         moments,
         span_lengths,
         distributed_loads,
         point_load_info,
         point_loads,
         x_coords
       ) do
    l_span = Nx.slice(span_lengths, [span_idx], [1]) |> Nx.squeeze()
    w_span = Nx.slice(distributed_loads, [span_idx], [1]) |> Nx.squeeze()
    m_left = Nx.slice(moments, [span_idx], [1]) |> Nx.squeeze()
    m_right = Nx.slice(moments, [span_idx + 1], [1]) |> Nx.squeeze()

    {points_per_span, _} = Nx.shape(x_coords)
    span_x = Nx.slice(x_coords, [0, span_idx], [points_per_span, 1]) |> Nx.squeeze()

    # Base shear: V0 = (M_left - M_right)/L - w*L/2
    v_base =
      m_left
      |> Nx.subtract(m_right)
      |> Nx.divide(l_span)
      |> Nx.subtract(Nx.multiply(w_span, l_span) |> Nx.divide(2.0))

    # Shear varies linearly with distributed load: V(x) = V0 - w*x
    shear_values =
      span_x
      |> Nx.multiply(w_span)
      |> Nx.subtract(v_base)
      |> Nx.negate()

    # Add point load contributions (step functions)
    add_point_load_shear_contributions(
      shear_values,
      span_idx,
      span_x,
      l_span,
      point_load_info,
      point_loads
    )
  end

  defp add_point_load_shear_contributions(
         shear_values,
         span_idx,
         _span_x,
         _l_span,
         point_load_info,
         point_loads
       ) do
    if Nx.size(point_loads) == 0 do
      shear_values
    else
      # Find point loads in this span
      span_mask = Nx.equal(point_load_info.spans, span_idx)

      if Nx.any(span_mask) |> Nx.to_number() == 0 do
        shear_values
      else
        # For each point load in this span, add step function contribution
        _masked_loads = Nx.select(span_mask, point_loads, 0.0)
        _masked_distances_left = Nx.select(span_mask, point_load_info.distances_left, 0.0)
        _masked_distances_right = Nx.select(span_mask, point_load_info.distances_right, 0.0)

        # This is a simplified implementation - full implementation would require
        # iterating through each point load and applying step functions
        # For now, return the base shear values
        shear_values
      end
    end
  end

  defp generate_moment_diagram(moments, shear_diagram, x_coords) do
    {points_per_span, n_spans} = Nx.shape(shear_diagram)

    moment_diagram = Nx.broadcast(0.0, {points_per_span, n_spans})

    moment_diagram =
      Enum.reduce(0..(n_spans - 1), moment_diagram, fn span_idx, acc_diagram ->
        span_moment = calculate_span_moment_diagram(span_idx, moments, shear_diagram, x_coords)
        Nx.put_slice(acc_diagram, [0, span_idx], Nx.reshape(span_moment, {:auto, 1}))
      end)

    {:ok, moment_diagram}
  end

  defp calculate_span_moment_diagram(span_idx, moments, shear_diagram, x_coords) do
    m_left = Nx.slice(moments, [span_idx], [1]) |> Nx.squeeze()
    {points_per_span, _} = Nx.shape(shear_diagram)
    span_shear = Nx.slice(shear_diagram, [0, span_idx], [points_per_span, 1]) |> Nx.squeeze()
    span_x = Nx.slice(x_coords, [0, span_idx], [points_per_span, 1]) |> Nx.squeeze()

    # Get the step size for integration
    dx =
      if Nx.size(span_x) > 1 do
        Nx.slice(span_x, [1], [1])
        |> Nx.squeeze()
        |> Nx.subtract(Nx.slice(span_x, [0], [1]) |> Nx.squeeze())
      else
        Nx.tensor(1.0)
      end

    # Integrate shear to get moment: M(x) = M_left + ∫(-V) dx
    # Using cumulative trapezoidal integration
    integrated_shear =
      span_shear
      |> Nx.negate()
      |> cumulative_trapz(dx)

    Nx.add(m_left, integrated_shear)
  end

  defp generate_slope_diagram(
         moment_diagram,
         moments_of_inertia,
         elastic_modulus,
         slopes,
         x_coords
       ) do
    {points_per_span, n_spans} = Nx.shape(moment_diagram)

    slope_diagram = Nx.broadcast(0.0, {points_per_span, n_spans})

    slope_diagram =
      Enum.reduce(0..(n_spans - 1), slope_diagram, fn span_idx, acc_diagram ->
        span_slope =
          calculate_span_slope_diagram(
            span_idx,
            moment_diagram,
            moments_of_inertia,
            elastic_modulus,
            slopes,
            x_coords
          )

        Nx.put_slice(acc_diagram, [0, span_idx], Nx.reshape(span_slope, {:auto, 1}))
      end)

    {:ok, slope_diagram}
  end

  defp calculate_span_slope_diagram(
         span_idx,
         moment_diagram,
         moments_of_inertia,
         elastic_modulus,
         slopes,
         x_coords
       ) do
    i_span = Nx.slice(moments_of_inertia, [span_idx], [1]) |> Nx.squeeze()
    slope_left = Nx.slice(slopes, [span_idx], [1]) |> Nx.squeeze()
    {points_per_span, _} = Nx.shape(moment_diagram)
    span_moment = Nx.slice(moment_diagram, [0, span_idx], [points_per_span, 1]) |> Nx.squeeze()
    span_x = Nx.slice(x_coords, [0, span_idx], [points_per_span, 1]) |> Nx.squeeze()

    # Get the step size for integration
    dx =
      if Nx.size(span_x) > 1 do
        Nx.slice(span_x, [1], [1])
        |> Nx.squeeze()
        |> Nx.subtract(Nx.slice(span_x, [0], [1]) |> Nx.squeeze())
      else
        Nx.tensor(1.0)
      end

    # Integrate moment to get slope: θ(x) = θ_left + ∫(M/(EI)) dx
    integrated_moment =
      span_moment
      |> Nx.divide(Nx.multiply(elastic_modulus, i_span))
      |> cumulative_trapz(dx)

    Nx.add(slope_left, integrated_moment)
  end

  defp generate_deflection_diagram(slope_diagram, x_coords) do
    {points_per_span, n_spans} = Nx.shape(slope_diagram)

    deflection_diagram = Nx.broadcast(0.0, {points_per_span, n_spans})

    deflection_diagram =
      Enum.reduce(0..(n_spans - 1), deflection_diagram, fn span_idx, acc_diagram ->
        span_deflection = calculate_span_deflection_diagram(span_idx, slope_diagram, x_coords)
        Nx.put_slice(acc_diagram, [0, span_idx], Nx.reshape(span_deflection, {:auto, 1}))
      end)

    {:ok, deflection_diagram}
  end

  defp calculate_span_deflection_diagram(span_idx, slope_diagram, x_coords) do
    {points_per_span, _} = Nx.shape(slope_diagram)
    span_slope = Nx.slice(slope_diagram, [0, span_idx], [points_per_span, 1]) |> Nx.squeeze()
    span_x = Nx.slice(x_coords, [0, span_idx], [points_per_span, 1]) |> Nx.squeeze()

    # Get the step size for integration
    dx =
      if Nx.size(span_x) > 1 do
        Nx.slice(span_x, [1], [1])
        |> Nx.squeeze()
        |> Nx.subtract(Nx.slice(span_x, [0], [1]) |> Nx.squeeze())
      else
        Nx.tensor(1.0)
      end

    # Integrate slope to get deflection: δ(x) = ∫θ dx
    cumulative_trapz(span_slope, dx)
  end

  defp flatten_coordinates(x_coords, span_lengths) do
    {_points_per_span, n_spans} = Nx.shape(x_coords)
    cumulative_lengths = Nx.cumulative_sum(span_lengths)

    # Adjust x-coordinates to be continuous across spans
    adjusted_coords =
      Enum.reduce(Range.new(1, n_spans - 1, 1), x_coords, fn span_idx, acc_coords ->
        offset = Nx.slice(cumulative_lengths, [span_idx - 1], [1]) |> Nx.squeeze()
        {points_per_span, _} = Nx.shape(acc_coords)
        span_coords = Nx.slice(acc_coords, [0, span_idx], [points_per_span, 1]) |> Nx.squeeze()
        adjusted_span = Nx.add(span_coords, offset)
        Nx.put_slice(acc_coords, [0, span_idx], Nx.reshape(adjusted_span, {:auto, 1}))
      end)

    Nx.flatten(adjusted_coords)
  end

  # Simple cumulative trapezoidal integration
  defp cumulative_trapz(y, dx) do
    if Nx.size(y) <= 1 do
      Nx.broadcast(0.0, Nx.shape(y))
    else
      # Simple cumulative sum approximation for trapezoidal rule
      y
      |> Nx.multiply(dx)
      |> Nx.cumulative_sum()
    end
  end
end
