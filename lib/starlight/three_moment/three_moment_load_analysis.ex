defmodule Starlight.ThreeMoment.LoadAnalysis do
  @moduledoc """
  Point load analysis and positioning for the Three-Moment Method.

  This module handles the identification of which span each point load is in
  and calculates the distances from each point load to the left and right supports.
  """

  alias Starlight.ThreeMoment.Types

  @doc """
  Analyzes point loads and determines their positions relative to spans.

  Converts MATLAB 1-based indexing to 0-based indexing for span identification.

  ## Parameters
  - `span_lengths`: Tensor of span lengths
  - `point_loads`: Tensor of point load magnitudes
  - `point_positions`: Tensor of point load positions from left end

  ## Returns
  - `{:ok, point_load_info()}` with span assignments and distances
  - `{:error, reason}` if point loads are outside the beam
  """
  @spec analyze_point_loads(Nx.Tensor.t(), Nx.Tensor.t(), Nx.Tensor.t(), boolean()) ::
          {:ok, Types.point_load_info()} | {:error, String.t()}
  def analyze_point_loads(span_lengths, _point_loads, point_positions, has_point_loads \\ true) do
    if not has_point_loads do
      {:ok,
       %{
         # Dummy value
         spans: Nx.tensor([0]),
         # Dummy value
         distances_left: Nx.tensor([0.0]),
         # Dummy value
         distances_right: Nx.tensor([0.0])
       }}
    else
      with {:ok, cumulative_lengths} <- calculate_cumulative_lengths(span_lengths),
           {:ok, spans} <- identify_spans(point_positions, cumulative_lengths),
           {:ok, {distances_left, distances_right}} <-
             calculate_distances(point_positions, spans, span_lengths, cumulative_lengths) do
        {:ok,
         %{
           spans: spans,
           distances_left: distances_left,
           distances_right: distances_right
         }}
      end
    end
  end

  @doc """
  Calculates cumulative span lengths for span identification.

  ## Parameters
  - `span_lengths`: Tensor of individual span lengths

  ## Returns
  - `{:ok, cumulative_lengths}` tensor of cumulative lengths
  """
  @spec calculate_cumulative_lengths(Nx.Tensor.t()) :: {:ok, Nx.Tensor.t()}
  def calculate_cumulative_lengths(span_lengths) do
    cumulative = Nx.cumulative_sum(span_lengths)
    {:ok, cumulative}
  end

  @doc """
  Identifies which span each point load is in (0-based indexing).

  Converts from MATLAB's 1-based span identification to 0-based.

  ## Parameters
  - `point_positions`: Tensor of point load positions
  - `cumulative_lengths`: Tensor of cumulative span lengths

  ## Returns
  - `{:ok, spans}` tensor of span indices (0-based)
  - `{:error, reason}` if any point is outside the beam
  """
  @spec identify_spans(Nx.Tensor.t(), Nx.Tensor.t()) ::
          {:ok, Nx.Tensor.t()} | {:error, String.t()}
  def identify_spans(point_positions, cumulative_lengths) do
    total_length = Nx.reduce_max(cumulative_lengths) |> Nx.to_number()
    max_position = Nx.reduce_max(point_positions) |> Nx.to_number()

    if max_position > total_length do
      {:error, "Point load position #{max_position} exceeds total beam length #{total_length}"}
    else
      # For each point position, find the first cumulative length that is greater
      # This gives us the span index (0-based)
      spans =
        point_positions
        |> Nx.new_axis(-1)
        |> Nx.less(Nx.new_axis(cumulative_lengths, 0))
        |> Nx.argmax(axis: 1)

      {:ok, spans}
    end
  end

  @doc """
  Calculates distances from point loads to left and right supports.

  ## Parameters
  - `point_positions`: Tensor of point load positions from left end
  - `spans`: Tensor of span indices for each point load (0-based)
  - `span_lengths`: Tensor of span lengths
  - `cumulative_lengths`: Tensor of cumulative span lengths

  ## Returns
  - `{:ok, {distances_left, distances_right}}` tuple of distance tensors
  """
  @spec calculate_distances(Nx.Tensor.t(), Nx.Tensor.t(), Nx.Tensor.t(), Nx.Tensor.t()) ::
          {:ok, {Nx.Tensor.t(), Nx.Tensor.t()}}
  def calculate_distances(point_positions, spans, _span_lengths, cumulative_lengths) do
    n_points = Nx.size(point_positions)

    if n_points == 0 do
      {:ok, {Nx.tensor([]), Nx.tensor([])}}
    else
      # Calculate distances to left support
      distances_left = calculate_left_distances(point_positions, spans, cumulative_lengths)

      # Calculate distances to right support
      distances_right = calculate_right_distances(point_positions, spans, cumulative_lengths)

      {:ok, {distances_left, distances_right}}
    end
  end

  # Private helper functions

  defp calculate_left_distances(point_positions, spans, cumulative_lengths) do
    # For span 0, distance to left is just the position
    # For other spans, distance to left is position minus cumulative length of previous spans

    # Create a tensor with cumulative lengths shifted by one position (with 0 at start)
    n_spans = Nx.size(cumulative_lengths)

    prev_cumulative =
      if n_spans > 1 do
        Nx.concatenate([Nx.tensor([0.0]), Nx.slice(cumulative_lengths, [0], [n_spans - 1])])
      else
        Nx.tensor([0.0])
      end

    # Get the previous cumulative length for each point's span
    span_starts = Nx.take(prev_cumulative, spans)

    # Distance to left support is position minus span start
    Nx.subtract(point_positions, span_starts)
  end

  defp calculate_right_distances(point_positions, spans, cumulative_lengths) do
    # Distance to right support is cumulative length at span end minus position
    span_ends = Nx.take(cumulative_lengths, spans)
    Nx.subtract(span_ends, point_positions)
  end
end
