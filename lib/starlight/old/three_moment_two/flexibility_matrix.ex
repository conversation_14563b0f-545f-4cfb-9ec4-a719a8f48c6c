defmodule Starlight.ThreeMomentTwo.FlexibilityMatrix do
  import Nx.Defn

  defn flexibility_matrix(l_span_lengths, i_moments_of_inertia) do
    # Create the flexibility matrix (8) - (10)
    # F = zeros(N+1, N+1); % Initialise the flexibility matrix

    # for j=2:N
    #     F(j, j-1) = L(j-1) / I(j-1);
    #     F(j, j) = 2 * (L(j-1) / I(j-1) + L(j) / I(j));
    #     F(j, j+1) = L(j) / I(j);
    # end
    #
    # F(1,1) = 1.0;
    # F(N+1,N+1) = 1.0;

    # Generated from ChatGPT based on the above code
    n = Nx.size(l_span_lengths)
    f = Nx.broadcast(0.0, {n + 1, n + 1})

    # j = 2..N in MATLAB
    j = Nx.iota({n - 1}) |> Nx.add(1)

    lj1 = Nx.take(l_span_lengths, j - 1)
    ij1 = Nx.take(i_moments_of_inertia, j - 1)
    lj = Nx.take(l_span_lengths, j)
    ij = Nx.take(i_moments_of_inertia, j)

    # Compute the three values for each row j
    left = lj1 / ij1
    diag = 2 * (lj1 / ij1 + lj / ij)
    right = lj / ij

    # Indices: build three index sets for f(j, j-1), f(j, j), f(j, j+1)
    idx_left = Nx.stack([j, j |> Nx.subtract(1)], axis: 1)
    idx_diag = Nx.stack([j, j], axis: 1)
    idx_right = Nx.stack([j, j |> Nx.add(1)], axis: 1)

    # Insert the values into the matrix
    f
    |> Nx.indexed_put(idx_left, left)
    |> Nx.indexed_put(idx_diag, diag)
    |> Nx.indexed_put(idx_right, right)

    # Set boundary conditions using zero-based indexing
    # F(1,1) = 1.0 in MATLAB becomes F[0,0] = 1.0
    |> Nx.put_slice([0, 0], Nx.tensor([[1.0]]))

    # F(N+1,N+1) = 1.0 in MATLAB becomes F[n,n] = 1.0
    |> Nx.put_slice([n, n], Nx.tensor([[1.0]]))
  end
end
