@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Import MapLibre GL JS CSS */
@import "maplibre-gl/dist/maplibre-gl.css";

/* This file is for your main application CSS */

/* Full-screen map utilities using Tailwind classes */
.map-fullscreen {
  @apply fixed top-0 left-0 right-0 bottom-0 w-screen h-screen z-[9999] !important;
}

.map-container {
  @apply w-full h-full relative;
}

/* Map-specific layout styles - only applied to map layout */
.map-layout-html {
  @apply h-full m-0 p-0 overflow-hidden;
}

.map-layout-body {
  @apply h-full m-0 p-0 overflow-hidden;
}

#map-container {
  @apply absolute top-0 left-0 right-0 bottom-0 w-screen h-screen;
}

/* Hide scrollbars on map layout only */
.map-layout-body ::-webkit-scrollbar {
  display: none;
}

/* MapLibre GL controls styling using Tailwind */
.maplibregl-ctrl-group {
  @apply bg-white/90 backdrop-blur-sm rounded-md shadow-lg !important;
}

.maplibregl-ctrl-group button {
  @apply bg-transparent border-0 !important;
}

.maplibregl-ctrl-group button:hover {
  @apply bg-black/5 !important;
}

/* MapLibre control positioning */
.maplibregl-ctrl-top-right {
  @apply top-2.5 right-2.5;
}

.maplibregl-ctrl-top-left {
  @apply top-2.5 left-2.5;
}

.maplibregl-ctrl-bottom-left {
  @apply bottom-2.5 left-2.5;
}

/* Style Switcher Control */
.style-switcher-control {
  @apply flex flex-col gap-0 !important;
}

.style-switcher-btn {
  @apply px-3 py-2 text-sm font-medium text-gray-700 bg-white border-0 cursor-pointer transition-all duration-200 !important;
  @apply hover:bg-blue-50 hover:text-blue-700 !important;
  @apply first:rounded-t-md last:rounded-b-md !important;
  @apply border-b border-gray-200 last:border-b-0 !important;
  min-width: 80px;
}

.style-switcher-btn.active {
  @apply bg-blue-600 text-white !important;
  @apply hover:bg-blue-700 !important;
}

.style-switcher-btn:focus {
  @apply outline-none ring-2 ring-blue-500 ring-opacity-50 !important;
}

/* Loading overlay for map style transitions */
.map-loading-overlay {
  @apply absolute inset-0 bg-gray-100 flex items-center justify-center z-50 transition-opacity duration-300;
}

.map-loading-overlay.hidden {
  @apply opacity-0 pointer-events-none;
}

.map-loading-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin;
}

.map-loading-text {
  @apply ml-3 text-gray-600 font-medium;
}
