import MAP_STYLES from './map_styles.js';

// Custom Style Switcher Control
class StyleSwitcherControl {
  constructor(hook) {
    this.hook = hook;
  }

  onAdd(map) {
    this.map = map;
    this.container = document.createElement('div');
    this.container.className = 'maplibregl-ctrl maplibregl-ctrl-group style-switcher-control';

    // Create buttons for each style
    Object.keys(MAP_STYLES).forEach(styleKey => {
      const button = document.createElement('button');
      button.className = `style-switcher-btn ${styleKey === this.hook.currentStyle ? 'active' : ''}`;
      button.type = 'button';
      button.title = `Switch to ${MAP_STYLES[styleKey].name} view`;
      button.textContent = MAP_STYLES[styleKey].name;
      button.setAttribute('data-style', styleKey);

      button.addEventListener('click', (e) => {
        e.preventDefault();
        this.switchToStyle(styleKey);
      });

      this.container.appendChild(button);
    });

    return this.container;
  }

  onRemove() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    this.map = undefined;
  }

  switchToStyle(styleKey) {
    // Update button states
    const buttons = this.container.querySelectorAll('.style-switcher-btn');
    buttons.forEach(btn => {
      btn.classList.toggle('active', btn.getAttribute('data-style') === styleKey);
    });

    // Switch the map style
    this.hook.switchStyle(styleKey);
  }
}

export default StyleSwitcherControl;
