// Import MapLibre GL JS - use require for better esbuild compatibility
import maplibregl from "maplibre-gl";

// Import extracted modules
import MAP_STYLES from "./map_styles.js";
import StyleSwitcherControl from "./style_switcher_control.js";

// Define the MapLibre hook as a separate object to ensure proper registration
const MapLibreHook = {
  mounted() {
    this.currentStyle = this.el.dataset.styleType || "standard";

    this.initializeMap();
  },

  initializeMap() {
    const center = JSON.parse(this.el.dataset.center);
    const zoom = parseFloat(this.el.dataset.zoom);
    const initialStyle = this.getMapStyle(this.currentStyle);

    // Initialize the map
    this.map = new maplibregl.Map({
      container: this.el,
      style: initialStyle,
      center: [center.lng, center.lat],
      zoom: zoom,
      attributionControl: true,
      logoPosition: "bottom-left",
    });

    // Add navigation controls
    this.map.addControl(new maplibregl.NavigationControl(), "top-right");

    // Add custom style switcher control
    this.map.addControl(new StyleSwitcherControl(this), "top-left");
  },

  // Get map style configuration
  getMapStyle(styleKey) {
    const styleConfig = MAP_STYLES[styleKey];
    if (!styleConfig) {
      console.warn(`Unknown style: ${styleKey}, falling back to standard`);
      return MAP_STYLES.standard.style;
    }

    return styleConfig.style;
  },

  // Switch map style
  switchStyle(newStyleKey) {
    const newStyle = this.getMapStyle(newStyleKey);

    // Use MapLibre's setStyle method
    this.map.setStyle(newStyle);

    // Update current style after initiating switch
    this.currentStyle = newStyleKey;

    // Notify LiveView about style change
    this.pushEvent("style_changed", { style: newStyleKey });
  },

  updated() {
    // console.log("MapLibre hook updated");
  },

  destroyed() {
    // console.log("MapLibre hook destroyed");

    if (this.map) {
      this.map.remove();
      this.map = null;
    }
  },
};

export default MapLibreHook;
