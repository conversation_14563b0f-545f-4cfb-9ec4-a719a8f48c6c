// Map style configurations
const MAP_STYLES = {
  standard: {
    name: 'Street',
    style: {
      version: 8,
      name: 'Street',
      metadata: {},
      sources: {
        'osm-source': {
          type: 'raster',
          tiles: [
            'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
          ],
          tileSize: 256,
          attribution: '© OpenStreetMap contributors'
        }
      },
      layers: [
        {
          id: 'osm-layer',
          type: 'raster',
          source: 'osm-source',
          minzoom: 0,
          maxzoom: 19
        }
      ]
    }
  },
  satellite: {
    name: 'Satellite',
    // Using a custom style with satellite imagery from multiple sources
    style: {
      version: 8,
      name: 'Satellite',
      metadata: {},
      sources: {
        'satellite-source': {
          type: 'raster',
          tiles: [
            'https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
            'https://mt2.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
            'https://mt3.google.com/vt/lyrs=s&x={x}&y={y}&z={z}'
          ],
          tileSize: 256,
          attribution: '© Google'
        }
      },
      layers: [
        {
          id: 'satellite-layer',
          type: 'raster',
          source: 'satellite-source',
          minzoom: 0,
          maxzoom: 20
        }
      ]
      // No glyphs or sprite needed for pure satellite imagery
    }
  },
  hybrid: {
    name: 'Hybrid',
    // Satellite imagery with street labels overlay
    style: {
      version: 8,
      name: 'Hybrid',
      metadata: {},
      sources: {
        'hybrid-source': {
          type: 'raster',
          tiles: [
            'https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}',
            'https://mt2.google.com/vt/lyrs=y&x={x}&y={y}&z={z}',
            'https://mt3.google.com/vt/lyrs=y&x={x}&y={y}&z={z}'
          ],
          tileSize: 256,
          attribution: '© Google'
        }
      },
      layers: [
        {
          id: 'hybrid-layer',
          type: 'raster',
          source: 'hybrid-source',
          minzoom: 0,
          maxzoom: 20
        }
      ]
      // No glyphs or sprite needed for raster-only layers
    }
  }
};

export default MAP_STYLES;
