# defmodule Starlight.ThreeMomentsTest do
#   use ExUnit.Case

#   alias Starlight.ThreeMomentOld

#   @moduledoc """
#   ExUnit tests for the ThreeMoment module.
#   Contains test cases for continuous beam analysis using the three-moment equation.
#   """

#   # Setup to run before each test
#   setup do
#     # Optional setup data
#     %{
#       tolerance_strict: 1.0e-3,
#       tolerance_approx: 0.05
#     }
#   end

#   test "three-span continuous beam with mixed loads", %{tolerance_approx: _tolerance} do
#     # Test parameters
#     # Three spans
#     l = [3.0, 5.0, 4.0]
#     # Constant moment of inertia
#     i = [2.0e-4, 2.0e-4, 2.0e-4]
#     # Young's modulus (N/m^2) - Steel
#     e = 2.0e11
#     # Varying uniform loads
#     w = [12000.0, 8000.0, 10000.0]
#     # Two point loads
#     p = [30000.0, 25000.0]
#     # Point loads at 1.5m and 10m from left
#     x = [1.5, 10.0]

#     ThreeMoment.three_moment(
#       Nx.tensor(l),
#       Nx.tensor(i),
#       Nx.tensor(e),
#       Nx.tensor(w),
#       Nx.tensor(p),
#       Nx.tensor(x)
#     )

#     # # Expected values for interior supports
#     # # First interior support
#     # expected_support1_moment = -15000.0
#     # # Second interior support
#     # expected_support2_moment = -20000.0

#     # # Run analysis
#     # result = ThreeMoment.analyze(l, i, e, w, p, x)

#     # # Interior support moments
#     # support1_moment = Enum.at(result.m, 1)
#     # support2_moment = Enum.at(result.m, 2)

#     # # For approximate values, use relative tolerance
#     # rel_diff1 = abs(support1_moment - expected_support1_moment) / abs(expected_support1_moment)
#     # rel_diff2 = abs(support2_moment - expected_support2_moment) / abs(expected_support2_moment)

#     # # Using slightly higher tolerance for complex case
#     # assert rel_diff1 < tolerance * 2,
#     #        "Three-span beam first interior support moment calculation failed. Expected #{expected_support1_moment}, got #{support1_moment}"

#     # # Using slightly higher tolerance for complex case
#     # assert rel_diff2 < tolerance * 2,
#     #        "Three-span beam second interior support moment calculation failed. Expected #{expected_support2_moment}, got #{support2_moment}"
#   end
# end
