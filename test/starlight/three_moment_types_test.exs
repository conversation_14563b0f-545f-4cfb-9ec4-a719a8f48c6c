defmodule Starlight.ThreeMoment.TypesTest do
  use ExUnit.Case, async: true

  alias <PERSON>light.ThreeMoment.Types

  describe "new_input_params/6" do
    test "creates valid input parameters with lists" do
      {:ok, params} =
        Types.new_input_params(
          [4.0, 6.0, 5.0],
          [1.0e-4, 1.2e-4, 1.0e-4],
          200.0e9,
          [10000.0, 15000.0, 12000.0],
          [50000.0],
          [2.0]
        )

      assert %{
               span_lengths: span_lengths,
               moments_of_inertia: moments_of_inertia,
               elastic_modulus: elastic_modulus,
               distributed_loads: distributed_loads,
               point_loads: point_loads,
               point_positions: point_positions
             } = params

      assert Nx.shape(span_lengths) == {3}
      assert Nx.shape(moments_of_inertia) == {3}
      assert elastic_modulus == 200.0e9
      assert Nx.shape(distributed_loads) == {3}
      assert Nx.shape(point_loads) == {1}
      assert Nx.shape(point_positions) == {1}
      assert params.has_point_loads == true
    end

    test "creates valid input parameters with empty point loads" do
      {:ok, params} =
        Types.new_input_params(
          [4.0, 6.0],
          [1.0e-4, 1.2e-4],
          200.0e9,
          [10000.0, 15000.0]
        )

      assert %{
               span_lengths: span_lengths,
               moments_of_inertia: moments_of_inertia,
               elastic_modulus: elastic_modulus,
               distributed_loads: distributed_loads,
               point_loads: point_loads,
               point_positions: point_positions
             } = params

      assert Nx.shape(span_lengths) == {2}
      assert Nx.shape(moments_of_inertia) == {2}
      assert elastic_modulus == 200.0e9
      assert Nx.shape(distributed_loads) == {2}
      # Dummy value
      assert Nx.shape(point_loads) == {1}
      # Dummy value
      assert Nx.shape(point_positions) == {1}
      assert params.has_point_loads == false
    end

    test "creates valid input parameters with Nx tensors" do
      span_lengths = Nx.tensor([4.0, 6.0])
      moments_of_inertia = Nx.tensor([1.0e-4, 1.2e-4])
      distributed_loads = Nx.tensor([10000.0, 15000.0])

      {:ok, params} =
        Types.new_input_params(
          span_lengths,
          moments_of_inertia,
          200.0e9,
          distributed_loads
        )

      assert %{
               span_lengths: ^span_lengths,
               moments_of_inertia: ^moments_of_inertia,
               elastic_modulus: 200.0e9,
               distributed_loads: ^distributed_loads,
               point_loads: point_loads,
               point_positions: point_positions
             } = params

      # Dummy value
      assert Nx.shape(point_loads) == {1}
      # Dummy value
      assert Nx.shape(point_positions) == {1}
      assert params.has_point_loads == false
    end

    test "validates dimension consistency" do
      {:error, reason} =
        Types.new_input_params(
          [4.0, 6.0],
          # Wrong length
          [1.0e-4],
          200.0e9,
          [10000.0, 15000.0]
        )

      assert reason == "moments_of_inertia must have same length as span_lengths"
    end

    test "validates distributed loads dimension consistency" do
      {:error, reason} =
        Types.new_input_params(
          [4.0, 6.0],
          [1.0e-4, 1.2e-4],
          200.0e9,
          # Wrong length
          [10000.0]
        )

      assert reason == "distributed_loads must have same length as span_lengths"
    end

    test "validates point load dimension consistency" do
      {:error, reason} =
        Types.new_input_params(
          [4.0, 6.0],
          [1.0e-4, 1.2e-4],
          200.0e9,
          [10000.0, 15000.0],
          [50000.0],
          # Wrong length
          [2.0, 3.0]
        )

      assert reason == "point_loads and point_positions must have same length"
    end

    test "validates positive span lengths" do
      {:error, reason} =
        Types.new_input_params(
          # Negative span length
          [4.0, -6.0],
          [1.0e-4, 1.2e-4],
          200.0e9,
          [10000.0, 15000.0]
        )

      assert reason == "span_lengths must contain only positive values"
    end

    test "validates positive moments of inertia" do
      {:error, reason} =
        Types.new_input_params(
          [4.0, 6.0],
          # Negative moment of inertia
          [1.0e-4, -1.2e-4],
          200.0e9,
          [10000.0, 15000.0]
        )

      assert reason == "moments_of_inertia must contain only positive values"
    end

    test "validates zero span lengths" do
      {:error, reason} =
        Types.new_input_params(
          # Zero span length
          [4.0, 0.0],
          [1.0e-4, 1.2e-4],
          200.0e9,
          [10000.0, 15000.0]
        )

      assert reason == "span_lengths must contain only positive values"
    end

    test "validates non-numeric input" do
      {:error, reason} =
        Types.new_input_params(
          # Non-numeric value
          [4.0, "invalid"],
          [1.0e-4, 1.2e-4],
          200.0e9,
          [10000.0, 15000.0]
        )

      assert reason == "span_lengths must contain only numbers"
    end

    test "validates invalid input types" do
      {:error, reason} =
        Types.new_input_params(
          # Not a list or tensor
          "invalid",
          [1.0e-4, 1.2e-4],
          200.0e9,
          [10000.0, 15000.0]
        )

      assert reason == "span_lengths must be a list or Nx tensor"
    end
  end
end
