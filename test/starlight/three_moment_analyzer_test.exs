defmodule Starlight.ThreeMomentAnalyzerTest do
  use ExUnit.Case, async: true

  alias Starlight.ThreeMomentAnalyzer
  alias Starlight.ThreeMoment.Types

  describe "analyze_beam/6" do
    test "analyzes a simple two-span beam with distributed loads only" do
      # Simple two-span beam: 4m + 6m spans
      span_lengths = [4.0, 6.0]
      moments_of_inertia = [1.0e-4, 1.2e-4]
      elastic_modulus = 200.0e9
      distributed_loads = [10000.0, 15000.0]
      point_loads = []
      point_positions = []

      {:ok, results} =
        ThreeMomentAnalyzer.analyze_beam(
          span_lengths,
          moments_of_inertia,
          elastic_modulus,
          distributed_loads,
          point_loads,
          point_positions
        )

      # Verify we get the expected structure
      assert %{
               moments: moments,
               reactions: reactions,
               shear_forces: shear_forces,
               x_coordinates: _x_coords,
               moment_diagram: _moment_diag,
               shear_diagram: _shear_diag,
               deflection_diagram: _deflection_diag,
               max_deflection: _max_def
             } = results

      # Check that we have the right number of moments (n_spans + 1 = 3)
      assert Nx.shape(moments) == {3}

      # Check that we have the right number of reactions (n_spans + 1 = 3)
      assert Nx.shape(reactions) == {3}

      # Check that we have shear forces for each span (2 x n_spans = 2x2)
      assert Nx.shape(shear_forces) == {2, 2}

      # Boundary conditions: moments at ends should be zero (simply supported)
      moment_values = Nx.to_list(moments)
      assert_in_delta List.first(moment_values), 0.0, 1.0e-10
      assert_in_delta List.last(moment_values), 0.0, 1.0e-10

      # Check equilibrium: sum of reactions should equal total load
      # w1*L1 + w2*L2
      total_load = 10000.0 * 4.0 + 15000.0 * 6.0
      reaction_sum = reactions |> Nx.sum() |> Nx.to_number()
      assert_in_delta reaction_sum, total_load, 1.0e-6
    end

    test "analyzes a beam with point loads" do
      # Single span beam with a point load
      span_lengths = [8.0]
      moments_of_inertia = [1.0e-4]
      elastic_modulus = 200.0e9
      # No distributed load
      distributed_loads = [0.0]
      # 50 kN point load
      point_loads = [50000.0]
      # At 3m from left end
      point_positions = [3.0]

      {:ok, results} =
        ThreeMomentAnalyzer.analyze_beam(
          span_lengths,
          moments_of_inertia,
          elastic_modulus,
          distributed_loads,
          point_loads,
          point_positions
        )

      # Check structure
      assert %{
               moments: moments,
               reactions: reactions,
               shear_forces: _shear_forces,
               x_coordinates: _x_coords,
               moment_diagram: _moment_diag,
               shear_diagram: _shear_diag,
               deflection_diagram: _deflection_diag,
               max_deflection: _max_def
             } = results

      # For single span: 2 moments, 2 reactions
      assert Nx.shape(moments) == {2}
      assert Nx.shape(reactions) == {2}

      # Boundary conditions: moments at ends should be zero
      moment_values = Nx.to_list(moments)
      assert_in_delta List.first(moment_values), 0.0, 1.0e-10
      assert_in_delta List.last(moment_values), 0.0, 1.0e-10

      # Check equilibrium: sum of reactions should equal point load
      reaction_sum = reactions |> Nx.sum() |> Nx.to_number()
      assert_in_delta reaction_sum, 50000.0, 1.0e-6
    end

    test "handles input validation errors" do
      # Mismatched array lengths
      span_lengths = [4.0, 6.0]
      # Wrong length
      moments_of_inertia = [1.0e-4]
      elastic_modulus = 200.0e9
      distributed_loads = [10000.0, 15000.0]

      {:error, reason} =
        ThreeMomentAnalyzer.analyze_beam(
          span_lengths,
          moments_of_inertia,
          elastic_modulus,
          distributed_loads
        )

      assert reason =~ "moments_of_inertia must have same length as span_lengths"
    end

    test "handles point load validation errors" do
      span_lengths = [4.0]
      moments_of_inertia = [1.0e-4]
      elastic_modulus = 200.0e9
      distributed_loads = [10000.0]
      point_loads = [50000.0]
      # Wrong length
      point_positions = [3.0, 5.0]

      {:error, reason} =
        ThreeMomentAnalyzer.analyze_beam(
          span_lengths,
          moments_of_inertia,
          elastic_modulus,
          distributed_loads,
          point_loads,
          point_positions
        )

      assert reason =~ "point_loads and point_positions must have same length"
    end
  end

  describe "analyze/1" do
    test "works with pre-validated input parameters" do
      {:ok, params} =
        Types.new_input_params(
          [4.0, 6.0],
          [1.0e-4, 1.2e-4],
          200.0e9,
          [10000.0, 15000.0],
          [],
          []
        )

      {:ok, results} = ThreeMomentAnalyzer.analyze(params)

      assert %{
               moments: _moments,
               reactions: _reactions,
               shear_forces: _shear_forces,
               x_coordinates: _x_coords,
               moment_diagram: _moment_diag,
               shear_diagram: _shear_diag,
               deflection_diagram: _deflection_diag,
               max_deflection: _max_def
             } = results
    end
  end
end
