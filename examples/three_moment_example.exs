# Three-Moment Method Example
# 
# This example demonstrates how to use the converted MATLAB three-moment
# analysis code in Elixir using the Nx library.

alias <PERSON>light.ThreeMomentAnalyzer

# Example 1: Simple two-span continuous beam with distributed loads
IO.puts("=== Example 1: Two-span beam with distributed loads ===")

# Beam properties
span_lengths = [4.0, 6.0]  # meters
moments_of_inertia = [1.0e-4, 1.2e-4]  # m^4
elastic_modulus = 200.0e9  # Pa (200 GPa for steel)
distributed_loads = [10000.0, 15000.0]  # N/m

# Analyze the beam
{:ok, results} = ThreeMomentAnalyzer.analyze_beam(
  span_lengths,
  moments_of_inertia,
  elastic_modulus,
  distributed_loads
)

IO.puts("Moments at supports:")
results.moments
|> Nx.to_list()
|> Enum.with_index()
|> Enum.each(fn {moment, i} ->
  IO.puts("  Support #{i}: #{:io_lib.format("~.2f", [moment])} N⋅m")
end)

IO.puts("\nReaction forces:")
results.reactions
|> Nx.to_list()
|> Enum.with_index()
|> Enum.each(fn {reaction, i} ->
  IO.puts("  Support #{i}: #{:io_lib.format("~.2f", [reaction])} N")
end)

# Verify equilibrium
total_load = 10000.0 * 4.0 + 15000.0 * 6.0
total_reaction = results.reactions |> Nx.sum() |> Nx.to_number()
IO.puts("\nEquilibrium check:")
IO.puts("  Total applied load: #{:io_lib.format("~.2f", [total_load])} N")
IO.puts("  Total reaction: #{:io_lib.format("~.2f", [total_reaction])} N")
IO.puts("  Difference: #{:io_lib.format("~.6f", [abs(total_load - total_reaction)])} N")

# Example 2: Single span beam with point load
IO.puts("\n=== Example 2: Single span beam with point load ===")

# Beam properties
span_lengths = [8.0]  # meters
moments_of_inertia = [1.0e-4]  # m^4
elastic_modulus = 200.0e9  # Pa
distributed_loads = [0.0]  # No distributed load
point_loads = [50000.0]  # 50 kN point load
point_positions = [3.0]  # At 3m from left end

# Analyze the beam
{:ok, results} = ThreeMomentAnalyzer.analyze_beam(
  span_lengths,
  moments_of_inertia,
  elastic_modulus,
  distributed_loads,
  point_loads,
  point_positions
)

IO.puts("Moments at supports:")
results.moments
|> Nx.to_list()
|> Enum.with_index()
|> Enum.each(fn {moment, i} ->
  IO.puts("  Support #{i}: #{:io_lib.format("~.2f", [moment])} N⋅m")
end)

IO.puts("\nReaction forces:")
results.reactions
|> Nx.to_list()
|> Enum.with_index()
|> Enum.each(fn {reaction, i} ->
  IO.puts("  Support #{i}: #{:io_lib.format("~.2f", [reaction])} N")
end)

# Verify equilibrium for point load
total_point_load = 50000.0
total_reaction = results.reactions |> Nx.sum() |> Nx.to_number()
IO.puts("\nEquilibrium check:")
IO.puts("  Total applied load: #{:io_lib.format("~.2f", [total_point_load])} N")
IO.puts("  Total reaction: #{:io_lib.format("~.2f", [total_reaction])} N")
IO.puts("  Difference: #{:io_lib.format("~.6f", [abs(total_point_load - total_reaction)])} N")

# Example 3: Three-span continuous beam
IO.puts("\n=== Example 3: Three-span continuous beam ===")

# Beam properties
span_lengths = [4.0, 6.0, 5.0]  # meters
moments_of_inertia = [1.0e-4, 1.2e-4, 1.0e-4]  # m^4
elastic_modulus = 200.0e9  # Pa
distributed_loads = [10000.0, 15000.0, 12000.0]  # N/m
point_loads = [50000.0, 30000.0]  # Point loads
point_positions = [2.0, 8.0]  # Positions from left end

# Analyze the beam
{:ok, results} = ThreeMomentAnalyzer.analyze_beam(
  span_lengths,
  moments_of_inertia,
  elastic_modulus,
  distributed_loads,
  point_loads,
  point_positions
)

IO.puts("Moments at supports:")
results.moments
|> Nx.to_list()
|> Enum.with_index()
|> Enum.each(fn {moment, i} ->
  IO.puts("  Support #{i}: #{:io_lib.format("~.2f", [moment])} N⋅m")
end)

IO.puts("\nReaction forces:")
results.reactions
|> Nx.to_list()
|> Enum.with_index()
|> Enum.each(fn {reaction, i} ->
  IO.puts("  Support #{i}: #{:io_lib.format("~.2f", [reaction])} N")
end)

# Verify equilibrium
total_distributed = 10000.0 * 4.0 + 15000.0 * 6.0 + 12000.0 * 5.0
total_point = 50000.0 + 30000.0
total_load = total_distributed + total_point
total_reaction = results.reactions |> Nx.sum() |> Nx.to_number()
IO.puts("\nEquilibrium check:")
IO.puts("  Total distributed load: #{:io_lib.format("~.2f", [total_distributed])} N")
IO.puts("  Total point load: #{:io_lib.format("~.2f", [total_point])} N")
IO.puts("  Total applied load: #{:io_lib.format("~.2f", [total_load])} N")
IO.puts("  Total reaction: #{:io_lib.format("~.2f", [total_reaction])} N")
IO.puts("  Difference: #{:io_lib.format("~.6f", [abs(total_load - total_reaction)])} N")

IO.puts("\n=== Analysis Complete ===")
IO.puts("The Elixir implementation successfully converts the MATLAB three-moment")
IO.puts("method to use Nx tensors with 0-based indexing while maintaining")
IO.puts("structural engineering accuracy and equilibrium.")
